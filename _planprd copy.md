# Техническое задание
## Платформа для получения данных с криптовалютных бирж

### Что мы строим

Создаем систему, которая:
- Подключается к разным биржам (Binance, Coinbase и другим)
- Собирает данные о ценах криптовалют в режиме реального времени
- Сохраняет историю цен и объемов торгов
- Предоставляет эти данные другим приложениям через API

### Для кого это нужно

**Основные пользователи:**
- Компании, которые делают трейдинг-приложения
- Разработчики торговых ботов
- Аналитические сервисы
- Частные трейдеры

### Основные функции

#### 1. Сбор данных с бирж
**Что делает:**
- Подключается к WebSocket биржам и получает данные в реальном времени
- Обрабатывает информацию о сделках, ценах, объемах
- Нормализует данные в единый формат

**Требования:**
- Поддержка минимум 5 крупных бирж
- Задержка получения данных не более 100 миллисекунд
- Автоматическое переподключение при сбоях

#### 2. Формирование свечей
**Что делает:**
- Создает свечи разных временных интервалов (1 минута, 5 минут, 1 час, 1 день)
- Каждая свеча содержит: цену открытия, максимум, минимум, закрытие, объем
- Сохраняет свечи в базу данных

**Требования:**
- Поддержка стандартных интервалов: 1m, 5m, 15m, 1h, 4h, 1d
- Точность данных 99.9%
- Автоматическое создание свечей без задержек

#### 3. Загрузка исторических данных
**Что делает:**
- Скачивает старые данные с бирж через REST API
- Заполняет пропуски в истории
- Проверяет целостность данных

**Требования:**
- История минимум за 2 года для популярных валютных пар
- Автоматическое обнаружение и исправление пропусков
- Приоритет загрузки по популярности пар

#### 4. API для пользователей
**Что делает:**
- Предоставляет REST API для получения данных
- WebSocket для real-time обновлений
- Система токенов и тарифных планов

**Требования:**
- Время отклика API менее 200 миллисекунд
- Поддержка до 10,000 одновременных WebSocket подключений
- Ограничения по количеству запросов в зависимости от тарифа

### Технические требования

#### Производительность
- **Пропускная способность**: обработка 100,000+ запросов в минуту
- **Задержка**: менее 100мс для получения свежих данных
- **Одновременные подключения**: до 10,000 WebSocket соединений
- **Объем данных**: до 1 миллиона сделок в секунду

#### Надежность
- **Время работы**: 99.9% (не более 8 часов простоя в год)
- **Резервное копирование**: автоматическое каждые 6 часов
- **Восстановление**: менее 15 минут при сбое
- **Мониторинг**: уведомления о проблемах в течение 2 минут

#### Масштабируемость
- **Горизонтальное масштабирование**: добавление серверов при росте нагрузки
- **База данных**: поддержка до 100TB данных
- **Новые биржи**: возможность добавления без остановки системы
- **Регионы**: развертывание в разных географических зонах

### Бизнес-требования

#### Тарифные планы
1. **Бесплатный**: 1000 запросов в час, данные с задержкой 1 минута
2. **Базовый**: 50,000 запросов в час, real-time данные, $29/месяц
3. **Профессиональный**: безлимитные запросы, все функции, $199/месяц

#### Монетизация
- Подписочная модель
- Дополнительные услуги (кастомные индикаторы, аналитика)
- API для институциональных клиентов

#### Метрики успеха
- **Доход**: $50,000 в месяц к концу года
- **Пользователи**: 1,000 активных клиентов
- **Качество данных**: менее 0.1% ошибок
- **Удовлетворенность**: оценка 4.5+ из 5

### Этапы разработки

#### Этап 1 (2 месяца) - MVP
- Подключение к 2 биржам (Binance, Coinbase)
- Базовый API для получения текущих цен
- Простое WebSocket подключение
- 10 популярных валютных пар

#### Этап 2 (1 месяц) - Исторические данные
- Загрузка истории за последний год
- API для получения исторических свечей
- Система заполнения пропусков

#### Этап 3 (1 месяц) - Дополнительные биржи
- Подключение еще 3 бирж
- Увеличение списка до 50 валютных пар
- Улучшение производительности

#### Этап 4 (1 месяц) - Продвинутые функции
- Технические индикаторы
- Система уведомлений
- Аналитические дашборды

### Риски и ограничения

#### Технические риски
- **Ограничения бирж**: лимиты на количество запросов
- **Нестабильность соединений**: частые разрывы WebSocket
- **Большой объем данных**: необходимость эффективного хранения

#### Бизнес-риски
- **Конкуренция**: много существующих решений
- **Правовое регулирование**: изменения в законодательстве
- **Зависимость от бирж**: изменения их политики

#### Способы снижения рисков
- Использование нескольких источников данных
- Кеширование и резервное копирование
- Мониторинг и автоматическое восстановление
- Юридическое сопровождение проекта

### Команда и ресурсы

#### Необходимые роли
- **Backend разработчик** (2 человека) - основная разработка
- **DevOps инженер** (1 человек) - настройка серверов и мониторинга
- **Тестировщик** (1 человек) - проверка качества
- **Продакт-менеджер** (1 человек) - координация и планирование

#### Инфраструктура
- **Серверы**: облачная платформа (AWS/GCP)
- **База данных**: PostgreSQL + TimescaleDB для временных рядов
- **Кеш**: Redis для быстрого доступа
- **Очереди**: DragonflyDB для обработки задач
- **Мониторинг**: системы логирования и метрик

### Критерии готовности

#### Функциональные критерии
- ✅ Подключение к 5 биржам работает стабильно
- ✅ API возвращает данные за время менее 200мс
- ✅ WebSocket отправляет обновления с задержкой менее 100мс
- ✅ История доступна за последние 2 года
- ✅ Система обрабатывает 100,000 запросов в час

#### Качественные критерии
- ✅ Покрытие тестами более 80%
- ✅ Документация по API готова
- ✅ Мониторинг настроен и работает
- ✅ Система восстановления после сбоев протестирована
- ✅ Нагрузочное тестирование пройдено успешно










# Пошаговый план разработки платформы

## Подготовительный этап (2 недели)

### Настройка инфраструктуры
**Что делаем:**
- Настраиваем серверы в облаке
- Устанавливаем базы данных (PostgreSQL, Redis, DragonflyDB)
- Настраиваем системы мониторинга
- Создаем репозиторий кода и настраиваем CI/CD

**Результат:** Готовая инфраструктура для разработки

## Этап 1: Минимальная рабочая версия (6 недель)

### Неделя 1-2: Основа системы
**Файлы для создания:**
- `main.go` - запуск приложения
- `config.yaml` - настройки системы
- `database/connection.go` - подключение к базе
- `database/models.go` - структуры данных
- `utils/logger.go` - система логирования

**Что получаем:** Приложение запускается и подключается к базе данных

### Неделя 3-4: Подключение к биржам
**Файлы для создания:**
- `exchanges/base_exchange.go` - общий интерфейс
- `exchanges/binance_client.go` - подключение к Binance
- `services/market_ingestion.go` - получение данных
- `queue/task_queue.go` - очередь задач

**Что получаем:** Система получает данные с Binance в реальном времени

### Неделя 5-6: Базовый API
**Файлы для создания:**
- `api/gateway.go` - обработка запросов
- `api/routes.go` - список URL
- `api/middleware.go` - проверки безопасности
- `services/data_processor.go` - обработка данных

**Что получаем:** Рабочий API для получения текущих цен

## Этап 2: WebSocket и исторические данные (4 недели)

### Неделя 7-8: WebSocket для реального времени
**Файлы для создания:**
- `api/websocket.go` - WebSocket соединения
- `cache/ticker_cache.go` - кеширование данных
- `workers/sync_worker.go` - быстрая обработка запросов

**Что получаем:** Пользователи получают обновления в реальном времени

### Неделя 9-10: Исторические данные
**Файлы для создания:**
- `services/historical_manager.go` - управление историей
- `workers/backfill_worker.go` - загрузка старых данных
- `database/queries.go` - запросы к базе данных

**Что получаем:** API возвращает исторические данные за любой период

## Этап 3: Масштабирование и дополнительные биржи (4 недели)

### Неделя 11-12: Новые биржи
**Файлы для создания:**
- `exchanges/coinbase_client.go` - подключение к Coinbase
- `exchanges/kraken_client.go` - подключение к Kraken
- `exchanges/exchange_manager.go` - управление всеми биржами

**Что получаем:** Данные с 3-4 крупных бирж

### Неделя 13-14: Улучшение производительности
**Файлы для создания:**
- `workers/health_worker.go` - контроль качества данных
- `utils/rate_limiter.go` - ограничение запросов
- `cache/candle_cache.go` - кеширование свечей

**Что получаем:** Система выдерживает высокие нагрузки

## Этап 4: Система пользователей и тарифы (3 недели)

### Неделя 15-16: Аутентификация
**Файлы для создания:**
- `auth/jwt_handler.go` - работа с токенами
- `auth/api_keys.go` - управление API ключами
- `auth/permissions.go` - проверка прав доступа

**Что получаем:** Система пользователей с разными правами доступа

### Неделя 17: Тарифные планы
**Файлы для доработки:**
- `api/middleware.go` - добавляем проверку тарифов
- `utils/rate_limiter.go` - ограничения по тарифам
- `database/models.go` - добавляем пользователей и тарифы

**Что получаем:** Разные тарифные планы с ограничениями

## Этап 5: Мониторинг и стабильность (3 недели)

### Неделя 18-19: Система мониторинга
**Файлы для создания:**
- `monitoring/metrics.go` - сбор статистики
- `monitoring/alerts.go` - уведомления о проблемах
- `utils/health_check.go` - проверка работоспособности

**Что получаем:** Полный контроль над работой системы

### Неделя 20: Финальные доработки
**Файлы для создания:**
- `workers/cleanup_worker.go` - очистка старых данных
- `services/notification_handler.go` - уведомления пользователям
- Полное тестирование системы

**Что получаем:** Готовая к продакшену система

## Критерии готовности каждого этапа

### Этап 1: MVP готов
- ✅ Система получает данные с одной биржи
- ✅ API возвращает текущие цены
- ✅ Данные сохраняются в базу
- ✅ Базовые тесты проходят

### Этап 2: Полнофункциональная версия
- ✅ WebSocket работает стабильно
- ✅ Доступны исторические данные за год
- ✅ Система автоматически заполняет пропуски
- ✅ Производительность соответствует требованиям

### Этап 3: Промышленная версия
- ✅ Подключены все основные биржи
- ✅ Система выдерживает нагрузку 100,000 запросов/час
- ✅ Мониторинг показывает стабильную работу
- ✅ Автоматическое восстановление после сбоев

### Этап 4: Коммерческая версия
- ✅ Система пользователей работает
- ✅ Тарифные планы настроены
- ✅ Биллинг интегрирован
- ✅ Документация готова

### Этап 5: Продакшен
- ✅ Мониторинг настроен полностью
- ✅ Все компоненты протестированы
- ✅ Система готова к коммерческому запуску
- ✅ Команда обучена поддержке

## Ресурсы по этапам

### Команда
- **1-2 этап**: 2 backend разработчика + 1 DevOps
- **3-4 этап**: + 1 тестировщик
- **5 этап**: + 1 продакт менеджер

### Инфраструктура
- **1 этап**: development окружение
- **2 этап**: staging окружение
- **3 этап**: production-ready инфраструктура
- **4-5 этап**: полная продакшен инфраструктура

### Бюджет (приблизительно)
- **Разработка**: $50,000 - $80,000 (зарплаты команды)
- **Инфраструктура**: $2,000 - $5,000/месяц
- **Сторонние сервисы**: $1,000 - $3,000/месяц
- **Общий бюджет на 5 месяцев**: $70,000 - $120,000

## Риски и их решения

### Технические риски
**Проблема**: Ограничения API бирж
**Решение**: Использование нескольких источников данных, кеширование

**Проблема**: Высокая нагрузка на базу данных
**Решение**: Оптимизация запросов, индексы, партиционирование

### Временные риски
**Проблема**: Задержки в разработке
**Решение**: Еженедельные ретроспективы, корректировка планов

**Проблема**: Сложности интеграции с биржами
**Решение**: Начать с самых надежных бирж, добавлять постепенно










# Детальная структура проекта финансовой платформы

```
crypto-data-platform/
├── main.go                          
│   # Точка входа в приложение. Читает конфигурацию из config.yaml, 
│   # инициализирует все сервисы (база данных, Redis, очереди), 
│   # запускает воркеры в отдельных горутинах, стартует HTTP сервер.
│   # При получении сигнала завершения - корректно останавливает все компоненты.
│
├── config.yaml                      
│   # Конфигурационный файл со всеми настройками: адреса баз данных, 
│   # API ключи бирж, порты сервисов, лимиты запросов, таймауты.
│   # Разные секции для development/staging/production окружений.
│
├── go.mod                          
│   # Определяет зависимости проекта: Fiber для HTTP, pgx для PostgreSQL,
│   # go-redis для Redis, gorilla/websocket и другие библиотеки.
│
├── go.sum                          
│   # Хеши всех зависимостей для обеспечения воспроизводимых сборок.
│
├── api/
│   ├── gateway.go                  
│   │   # HTTP маршрутизатор принимающий все внешние запросы. Проверяет JWT токены,
│   │   # определяет тарифный план пользователя, применяет rate limiting,
│   │   # маршрутизирует запросы к нужным сервисам. При запросе недоступных данных
│   │   # создает задачу в очереди для их получения и возвращает статус "syncing".
│   │
│   ├── websocket.go                
│   │   # Управляет WebSocket соединениями. При подключении клиента сохраняет
│   │   # информацию о подписках в Redis, слушает очередь событий и отправляет
│   │   # обновления подписанным клиентам. Обрабатывает отключения, переподключения.
│   │   # Группирует клиентов по интересующим их валютным парам для эффективной рассылки.
│   │
│   ├── middleware.go               
│   │   # Промежуточные обработчики запросов. Проверяет валидность JWT токенов,
│   │   # извлекает информацию о пользователе, проверяет права доступа к эндпоинтам,
│   │   # применяет rate limiting на основе тарифного плана, логирует все запросы,
│   │   # обрабатывает CORS заголовки для браузерных клиентов.
│   │
│   └── routes.go                   
│       # Определяет все HTTP эндпоинты системы: GET /api/v1/tickers для списка валют,
│       # GET /api/v1/candles/{symbol} для свечей, WebSocket /ws для real-time данных.
│       # Связывает URL с соответствующими обработчиками, применяет middleware.
│
├── services/
│   ├── market_ingestion.go         
│   │   # Менеджер подключений к биржам. Устанавливает WebSocket соединения с Binance,
│   │   # Coinbase и другими биржами, подписывается на потоки данных по всем валютным
│   │   # парам. При получении данных о сделке - нормализует формат, валидирует,
│   │   # публикует событие "новые данные получены" в очередь. Обрабатывает разрывы
│   │   # соединений, переподключается автоматически, ведет статистику качества данных.
│   │
│   ├── data_processor.go           
│   │   # Обработчик сырых рыночных данных. Слушает очередь событий "новые данные получены",
│   │   # группирует сделки по временным интервалам (1м, 5м, 1ч), создает OHLCV свечи,
│   │   # рассчитывает технические индикаторы (скользящие средние, RSI, MACD).
│   │   # Сохраняет готовые свечи в базу данных и кеш, публикует событие "свеча готова".
│   │
│   ├── historical_manager.go       
│   │   # Управляет историческими данными. Слушает события "нужны исторические данные",
│   │   # определяет какие периоды отсутствуют, создает задачи для загрузки через REST API бирж.
│   │   # Проверяет целостность данных, обнаруживает пропуски, запускает backfill процессы.
│   │   # Управляет приоритетами: популярные пары загружает первыми, редкие - в фоне.
│   │
│   ├── task_scheduler.go           
│   │   # Планировщик фоновых задач. По расписанию создает задачи: каждые 15 секунд проверяет
│   │   # здоровье подключений к биржам, каждые 5 минут запускает проверку качества данных,
│   │   # раз в час создает задачи для загрузки истории редких валютных пар, ночью запускает
│   │   # очистку старых данных. Все задачи помещает в приоритетную очередь.
│   │
│   └── notification_handler.go     
│       # Система уведомлений. Слушает события "отправить уведомление", определяет способ
│       # доставки (email, webhook, push), форматирует сообщения по шаблонам, отправляет
│       # через внешние сервисы. Обрабатывает ошибки доставки, повторяет отправку,
│       # ведет статистику доставляемости, управляет подписками пользователей.
│
├── workers/
│   ├── sync_worker.go              
│   │   # Высокоприоритетный воркер для срочных задач. Слушает очередь, при появлении
│   │   # задачи "синхронизировать тикер" немедленно подключается к REST API биржи,
│   │   # скачивает недостающие данные за запрошенный период, обрабатывает и сохраняет
│   │   # в базу. Уведомляет API Gateway о готовности данных через WebSocket.
│   │   # Работает с максимальной скоростью, игнорируя rate limiting бирж когда возможно.
│   │
│   ├── backfill_worker.go          
│   │   # Фоновый воркер для заполнения истории. Берет задачи низкого приоритета из очереди,
│   │   # медленно и аккуратно скачивает исторические данные порциями, соблюдая лимиты бирж.
│   │   # Обрабатывает большие временные промежутки частями, сохраняет прогресс,
│   │   # может быть остановлен и перезапущен без потери данных. Работает круглосуточно в фоне.
│   │
│   ├── health_worker.go            
│   │   # Контролер качества данных. Проверяет что от всех активных тикеров поступают
│   │   # свежие данные, выявляет "мертвые" подключения к биржам, обнаруживает аномалии
│   │   # в ценах (слишком большие скачки), проверяет целостность свечей, сверяет данные
│   │   # между разными биржами. При обнаружении проблем создает задачи на исправление.
│   │
│   └── cleanup_worker.go           
│       # Уборщик старых данных. По расписанию анализирует базу данных, удаляет данные
│       # старше retention периода (например, минутные свечи старше года), архивирует
│       # редко используемые данные, освобождает место в кеше, оптимизирует индексы
│       # базы данных, сжимает таблицы. Работает в периоды низкой нагрузки.
│
├── database/
│   ├── connection.go               
│   │   # Менеджер соединений с PostgreSQL. Создает пул соединений с настройками
│   │   # для максимальной производительности, обрабатывает reconnect при сбоях,
│   │   # мониторит количество активных соединений, настраивает таймауты,
│   │   # обеспечивает graceful shutdown при остановке приложения.
│   │
│   ├── migrations/                 
│   │   ├── 001_create_tickers.sql  
│   │   │   # SQL для создания таблицы tickers: id, symbol (BTC/USDT), exchange,
│   │   │   # status (active/inactive), created_at, updated_at, metadata (JSON с доп. info).
│   │   │   # Индексы по symbol и status для быстрого поиска активных тикеров.
│   │   │
│   │   ├── 002_create_candles.sql  
│   │   │   # SQL для создания гипертаблицы candles в TimescaleDB: ticker_id, timestamp,
│   │   │   # open, high, low, close, volume, interval_type. Партиционирование по времени
│   │   │   # для оптимального хранения временных рядов. Индексы для быстрых запросов.
│   │   │
│   │   ├── 003_create_tasks.sql    
│   │   │   # SQL для таблицы задач: id, type, priority, payload (JSON), status,
│   │   │   # created_at, started_at, completed_at, error_message, retry_count.
│   │   │   # Индексы по priority и status для эффективной обработки очереди.
│   │   │
│   │   ├── 004_create_users.sql    
│   │   │   # SQL для пользователей: id, email, password_hash, api_key, plan_type,
│   │   │   # rate_limit, created_at, last_login. Уникальные индексы по email и api_key.
│   │   │
│   │   └── 005_create_analytics.sql
│   │       # SQL для аналитики: таблицы для хранения метрик использования API,
│   │       # статистики по пользователям, логов запросов для биллинга и аналитики.
│   │
│   ├── models.go                   
│   │   # Go структуры данных точно соответствующие таблицам базы: Ticker, Candle, Task, User.
│   │   # Методы для валидации данных, JSON сериализации, преобразования между форматами.
│   │   # Встроенные методы для работы с базой: Create(), Update(), Delete(), Find().
│   │
│   └── queries.go                  
│       # Оптимизированные SQL запросы для частых операций: GetCandlesByPeriod,
│       # FindGapsInHistory, GetActiveTickersList, BulkInsertCandles.
│       # Параметризованные запросы для защиты от SQL инъекций, подготовленные statements
│       # для максимальной производительности, batch операции для массовых вставок.
│
├── cache/
│   ├── redis_client.go             
│   │   # Менеджер Redis соединений. Настраивает cluster/sentinel подключение,
│   │   # обрабатывает failover, настраивает connection pool, реализует retry logic,
│   │   # мониторит производительность кеша, обеспечивает graceful degradation
│   │   # при недоступности Redis (fallback на базу данных).
│   │
│   ├── ticker_cache.go             
│   │   # Кеширование метаданных валютных пар. Сохраняет список активных тикеров,
│   │   # текущие цены, 24ч статистику в Redis с TTL 30 секунд. При запросе сначала
│   │   # проверяет кеш, если данных нет - обращается к базе, обновляет кеш.
│   │   # Инвалидация кеша при получении новых данных с бирж.
│   │
│   └── candle_cache.go             
│       # Кеширование свечей для быстрого доступа. Сохраняет последние 1000 свечей
│       # по каждому тикеру и интервалу, сжимает данные для экономии памяти,
│       # реализует LRU eviction для управления размером кеша, batch обновления
│       # для эффекивности, separate cache keys для разных временных интервалов.
│
├── exchanges/
│   ├── base_exchange.go            
│   │   # Интерфейс Exchange определяющий общие методы для всех бирж: Connect(),
│   │   # Subscribe(), GetHistoricalData(), GetSymbols(). Базовая структура с общей
│   │   # логикой: rate limiting, error handling, reconnection, data normalization.
│   │   # Метрики производительности, логирование всех операций.
│   │
│   ├── binance_client.go           
│   │   # Реализация клиента для Binance API. WebSocket подключение к потокам данных,
│   │   # обработка специфичных для Binance форматов сообщений, соблюдение их rate limits,
│   │   # REST API для исторических данных, обработка ошибок соединения, mapping
│   │   # символов Binance в наш унифицированный формат, keep-alive механизм.
│   │
│   ├── coinbase_client.go          
│   │   # Клиент для Coinbase Pro/Advanced API. Подключение к их WebSocket feed,
│   │   # обработка их JSON формата, OAuth аутентификация для приватных данных,
│   │   # pagination для исторических запросов, обработка их специфичных ошибок,
│   │   # преобразование их символов (BTC-USD -> BTC/USD), sandbox режим для тестов.
│   │
│   ├── kraken_client.go            
│   │   # Интеграция с Kraken API. Их специфичная WebSocket аутентификация,
│   │   # обработка их сложных JSON структур, работа с их rate limiting,
│   │   # особенности их REST API для истории, обработка downtime их сервисов.
│   │
│   └── exchange_manager.go         
│       # Координатор всех подключений к биржам. Запускает клиентов в отдельных горутинах,
│       # агрегирует данные от всех источников, детектирует расхождения в ценах между биржами,
│       # управляет приоритетами источников данных, обеспечивает fallback при сбоях,
│       # балансирует нагрузку между биржами, ведет статистику качества данных по источникам.
│
├── queue/
│   ├── task_queue.go               
│   │   # Реализация очереди задач в DragonflyDB. Методы AddTask(), GetNextTask(),
│   │   # MarkCompleted(), RetryFailed(). Обеспечивает exactly-once delivery,
│   │   # поддерживает приоритеты задач, автоматический retry с exponential backoff,
│   │   # dead letter queue для проблемных задач, мониторинг размера очереди.
│   │
│   ├── priority_queue.go           
│   │   # Управление приоритетами задач. Высокий приоритет для пользовательских запросов,
│   │   # средний для регулярной синхронизации, низкий для фонового backfill.
│   │   # Round-robin между приоритетами для справедливости, starvation prevention,
│   │   # динамическое изменение приоритетов на основе нагрузки системы.
│   │
│   └── event_bus.go                
│       # Pub/Sub система для событий между компонентами. События: TickerDataReceived,
│       # CandleProcessed, UserConnected, AlertTriggered. Гарантированная доставка,
│       # subscribe patterns для групп событий, async/sync обработка,
│       # event sourcing для audit trail, replay mechanism для восстановления состояния.
│
├── utils/
│   ├── logger.go                   
│   │   # Структурированное логирование с уровнями DEBUG, INFO, WARN, ERROR.
│   │   # Ротация файлов логов, отправка критичных ошибок в Slack/email,
│   │   # JSON формат для интеграции с ELK stack, контекстная информация
│   │   # (request_id, user_id), performance logging, разные выводы для dev/prod.
│   │
│   ├── validator.go                
│   │   # Валидация входящих данных. Проверка корректности символов валютных пар,
│   │   # временных интервалов, дат, API ключей, JWT токенов. Sanitization входящих данных,
│   │   # защита от injection атак, нормализация форматов данных, custom validation rules
│   │   # для финансовых данных (цены > 0, объемы >= 0).
│   │
│   ├── rate_limiter.go             
│   │   # Реализация rate limiting на базе token bucket или sliding window алгоритмов.
│   │   # Разные лимиты для разных тарифных планов, distributed rate limiting через Redis,
│   │   # graceful degradation при превышении лимитов, whitelist для внутренних сервисов,
│   │   # динамические лимиты на основе системной нагрузки.
│   │
│   └── health_check.go             
│       # Health check эндпоинты для load balancer и monitoring. Проверка доступности
│       # базы данных, Redis, очередей задач, внешних API. Liveness и readiness probes
│       # для Kubernetes, detailed health status с метриками производительности,
│       # graceful shutdown handling, dependency health aggregation.
│
├── monitoring/
│   ├── metrics.go                  
│   │   # Сбор метрик Prometheus: количество запросов, время ответа, ошибки,
│   │   # активные подключения, размер очередей, использование памяти.
│   │   # Custom metrics для бизнес-логики: количество обработанных свечей,
│   │   # качество данных, coverage по биржам, SLA метрики.
│   │
│   ├── alerts.go                   
│   │   # Система алертов на основе метрик. Уведомления в Slack при высоком error rate,
│   │   # email при недоступности критичных сервисов, PagerDuty для production incidents.
│   │   # Escalation rules, alert correlation для снижения noise, automated remediation
│   │   # для простых проблем, dashboard integration.
│   │
│   └── dashboard.go                
│       # HTTP эндпоинты для dashboard с метриками системы в реальном времени.
│       # Графики производительности, статус всех компонентов, active alerts,
│       # бизнес-метрики (активные пользователи, популярные пары), система статистики
│       # для анализа тенденций, export метрик в внешние системы мониторинга.
│
├── auth/
│   ├── jwt_handler.go              
│   │   # Создание и валидация JWT токенов. Методы GenerateToken(), ValidateToken(),
│   │   # RefreshToken(). Поддержка claims с информацией о пользователе и правах,
│   │   # blacklist отозванных токенов в Redis, короткий TTL для access tokens,
│   │   # longer TTL для refresh tokens, rotation strategy для безопасности.
│   │
│   ├── permissions.go              
│   │   # RBAC система проверки прав доступа. Роли: free_user, premium_user, admin.
│   │   # Права доступа к эндпоинтам, data access controls, resource-based permissions,
│   │   # временные права доступа, audit logging всех проверок прав,
│   │   # integration с external identity providers при необходимости.
│   │
│   └── api_keys.go                 
│       # Управление API ключами пользователей. Генерация secure random keys,
│       # хеширование и безопасное хранение, rotation механизм, scoped permissions
│       # (read-only, write access), usage tracking для биллинга, revocation list,
│       # integration с rate limiter для enforcement лимитов по ключам.
│
├── tests/
│   ├── integration/                
│   │   ├── api_test.go             
│   │   │   # End-to-end тесты API эндпоинтов. Тестирование полного flow:
│   │   │   # аутентификация -> запрос данных -> обработка -> ответ. Проверка
│   │   │   # корректности JSON responses, HTTP status codes, rate limiting,
│   │   │   # WebSocket functionality, error handling scenarios.
│   │   │
│   │   └── data_flow_test.go       
│   │       # Тестирование прохождения данных через всю систему: mock биржа
│   │       # отправляет данные -> ingestion -> processing -> storage -> API.
│   │       # Проверка целостности данных, производительности, обработки ошибок,
│   │       # восстановления после сбоев, консистентности кеша и базы.
│   │
│   └── unit/                       
│       ├── services_test.go        
│       │   # Unit tests для всех сервисов: market_ingestion, data_processor,
│       │   # historical_manager. Mocking внешних зависимостей (база, Redis, биржи),
│       │   # тестирование edge cases, error scenarios, performance boundaries,
│       │   # data validation logic, concurrent access scenarios.
│       │
│       └── workers_test.go         
│           # Тесты воркеров с mock очередью задач. Проверка правильности обработки
│           # разных типов задач, retry logic, error handling, graceful shutdown,
│           # resource cleanup, task prioritization, concurrent task processing.
│
├── docs/
│   ├── api_documentation.md        
│   │   # Полная документация REST API: описание всех эндпоинтов, параметров,
│   │   # response formats, error codes, rate limits, authentication methods.
│   │   # OpenAPI/Swagger specification, code examples для популярных языков,
│   │   # WebSocket protocol documentation, SDK documentation если есть.
│   │
│   ├── deployment_guide.md         
│   │   # Пошаговая инструкция развертывания системы в разных окружениях.
│   │   # Docker-compose для локальной разработки, Kubernetes manifests для prod,
│   │   # настройка баз данных, миграции, секреты, мониторинг, backup стратегии,
│   │   # rollback procedures, scaling guidelines, troubleshooting common issues.
│   │
│   ├── architecture_overview.md    
│   │   # Высокоуровневое описание архитектуры системы с диаграммами.
│   │   # Data flow diagrams, component interaction, scaling strategies,
│   │   # design decisions rationale, performance characteristics,
│   │   # security considerations, future roadmap и planned improvements.
│   │
│   └── runbook.md                  
│       # Операционное руководство для поддержки системы в production.
│       # Типичные проблемы и их решения, процедуры восстановления,
│       # monitoring dashboards guide, alert response procedures,
│       # maintenance tasks, backup/restore procedures, incident response.
│
├── scripts/
│   ├── setup_database.sh           
│   │   # Скрипт инициализации базы данных: создание пользователей, баз данных,
│   │   # настройка TimescaleDB extension, применение миграций, создание индексов,
│   │   # настройка backup jobs, performance tuning параметров PostgreSQL,
│   │   # проверка готовности базы к работе, seed данные для разработки.
│   │
│   ├── deploy.sh                   
│   │   # Автоматизированный скрипт развертывания: сборка Docker образов,
│   │   # push в registry, обновление Kubernetes deployments, rolling updates,
│   │   # health checks после деплоя, rollback при ошибках, уведомления
│   │   # в Slack о статусе деплоя, integration с CI/CD pipeline.
│   │
│   ├── backup.sh                   
│   │   # Создание резервных копий: dump PostgreSQL базы с compression,
│   │   # backup Redis snapshots, архивация логов, upload в S3/cloud storage,
│   │   # retention policy для старых backup, проверка целостности backup,
│   │   # automated restore testing, notifications о статусе backup.
│   │
│   ├── monitor.sh                  
│   │   # Скрипт мониторинга системы: проверка доступности всех компонентов,
│   │   # disk space usage, memory consumption, network connectivity,
│   │   # database connections, queue sizes, external API availability,
│   │   # генерация daily/weekly reports, integration с внешними monitoring системами.
│   │
│   └── stress_test.sh              
│       # Нагрузочное тестирование системы: симуляция высокой нагрузки API,
│       # множественные WebSocket соединения, bulk data requests,
│       # проверка производительности под нагрузкой, memory leaks detection,
│       # автоматическое масштабирование testing, результаты в readable формате.
│
└── docker/
    ├── Dockerfile                  
    │   # Multi-stage Docker build: builder stage с Go compiler и dependencies,
    │   # production stage с minimal Alpine Linux, security updates,
    │   # non-root user для безопасности, health check endpoint,
    │   # proper signal handling для graceful shutdown, оптимизация размера образа.
    │
    ├── docker-compose.yml          
    │   # Локальная разработка environment: все сервисы (PostgreSQL, Redis,
    │   # DragonflyDB, приложение), volume mounting для hot reload,
    │   # environment variables для разных конфигураций, networking между контейнерами,
    │   # port mapping для доступа с host машины, зависимости между сервисами.
    │
    ├── docker-compose.prod.yml     
    │   # Production-ready compose file: optimized resource limits,
    │   # secrets management, proper restart policies, health checks,
    │   # logging configuration, backup volumes, monitoring sidecars,
    │   # load balancer configuration, SSL/TLS termination setup.
    │
    ├── nginx.conf                  
    │   # Reverse proxy конфигурация: load balancing между app instances,
    │   # SSL termination, rate limiting, static files serving,
    │   # WebSocket proxy support, caching headers для статики,
    │   # security headers, compression, access logging для аналитики.
    │
    └── k8s/                        
        ├── namespace.yaml          
        │   # Kubernetes namespace для изоляции ресурсов проекта,
        │   # resource quotas, network policies, default service account.
        │
        ├── configmap.yaml          
        │   # ConfigMap с настройками приложения: database URLs,
        │   # external API endpoints, feature flags, logging levels,
        │   # cache settings, разные конфиги для staging/production.
        │
        ├── secret.yaml             
        │   # Kubernetes secrets: database passwords, API keys бирж,
        │   # JWT signing keys, external service credentials,
        │   # TLS certificates, encrypted at rest, rotation procedures.
        │
        ├── deployment.yaml         
        │   # Deployment манифест: replica count, resource limits/requests,
        │   # health checks, rolling update strategy, environment variables,
        │   # volume mounts, affinity rules, security context.
        │
        ├── service.yaml            
        │   # Service для внутреннего доступа к pods: port mapping,
        │   # service discovery, load balancing algorithm,
        │   # session affinity если нужно.
        │
        ├── ingress.yaml            
        │   # Ingress для внешнего доступа: SSL termination,
        │   # path-based routing, rate limiting annotations,
        │   # authentication integration, custom error pages.
        │
        ├── hpa.yaml                
        │   # Horizontal Pod Autoscaler: автоматическое масштабирование
        │   # на основе CPU/memory utilization, custom metrics,
        │   # min/max replicas, scale up/down policies.
        │
        └── pdb.yaml                
            # Pod Disruption Budget: обеспечение availability во время
            # maintenance operations, min available pods, disruption windows.

## Дополнительные конфигурационные файлы

├── .env.example                    
│   # Пример файла environment variables: все необходимые переменные
│   # с описаниями и example values для локальной разработки,
│   # production secrets заменены на placeholder values,
│   # группировка по функциональности (database, cache, external APIs).
│
├── .gitignore                      
│   # Git ignore file: исключение vendor/, binary files, IDE configs,
│   # local environment files, временные файлы, OS-specific files,
│   # debug outputs, coverage reports, build artifacts.
│
├── Makefile                        
│   # Automation commands: make build, make test, make deploy,
│   # make migrate, make lint, make docker-build, make k8s-deploy,
│   # удобные shortcuts для разработчиков, integration с CI/CD.
│
├── .github/workflows/              
│   ├── ci.yml                      
│   │   # GitHub Actions CI pipeline: automated testing на pull requests,
│   │   # code linting, security scanning, build verification,
│   │   # integration tests, test coverage reporting, quality gates.
│   │
│   ├── cd.yml                      
│   │   # Continuous Deployment: автоматический deploy на staging
│   │   # при merge в main branch, manual approval для production,
│   │   # rollback capabilities, deployment notifications.
│   │
│   └── security.yml                
│       # Security scanning: dependency vulnerability checks,
│       # SAST/DAST scanning, Docker image scanning,
│       # secret detection, license compliance checks.
│
└── configs/                        
    ├── prometheus.yml              
    │   # Prometheus конфигурация: scrape targets, scrape intervals,
    │   # metric relabeling, alerting rules, service discovery,
    │   # retention policies, remote write configuration.
    │
    ├── grafana/                    
    │   ├── dashboards/             
    │   │   ├── system_overview.json # Dashboard системных метрик
    │   │   ├── business_metrics.json # Dashboard бизнес-метрик
    │   │   └── api_performance.json # Dashboard производительности API
    │   │
    │   └── datasources.yml         
    │       # Grafana data sources: Prometheus connection,
    │       # PostgreSQL для business queries, Loki для logs.
    │
    ├── alertmanager.yml            
    │   # Alertmanager configuration: notification channels (Slack, email),
    │   # alert routing rules, grouping, silencing rules,
    │   # escalation policies, webhook integrations.
    │
    └── jaeger.yml                  
        # Distributed tracing configuration: sampling strategies,
        # storage backend (Elasticsearch), UI configuration,
        # performance tuning для high-throughput systems.
```

## Описание ключевых взаимодействий между компонентами

### Поток данных в реальном времени
1. **exchanges/binance_client.go** подключается к WebSocket Binance, получает tick данные
2. **services/market_ingestion.go** нормализует данные и публикует событие в **queue/event_bus.go**
3. **services/data_processor.go** слушает события, группирует ticks в OHLCV свечи
4. **workers/sync_worker.go** сохраняет свечи в PostgreSQL через **database/queries.go**
5. **cache/candle_cache.go** обновляет Redis кеш для быстрого доступа
6. **api/websocket.go** уведомляет подключенных клиентов о новых данных

### Обработка пользовательского запроса
1. **api/gateway.go** получает HTTP запрос на исторические данные
2. **api/middleware.go** проверяет JWT токен через **auth/jwt_handler.go**
3. **auth/permissions.go** проверяет права доступа к запрашиваемым данным
4. **cache/candle_cache.go** проверяет наличие данных в Redis кеше
5. Если данных нет - **database/queries.go** запрашивает PostgreSQL
6. Если период неполный - **queue/task_queue.go** создает задачу для **workers/backfill_worker.go**
7. **api/gateway.go** возвращает доступные данные + статус "syncing"

### Фоновая загрузка исторических данных
1. **services/task_scheduler.go** по расписанию создает задачи backfill
2. **queue/priority_queue.go** управляет очередностью обработки задач
3. **workers/backfill_worker.go** берет задачу, определяет missing periods
4. **exchanges/exchange_manager.go** выбирает лучший источник данных
5. **exchanges/coinbase_client.go** делает REST API запросы порциями
6. **services/historical_manager.go** валидирует и нормализует данные
7. **database/connection.go** сохраняет в PostgreSQL batch операциями
8. **monitoring/metrics.go** обновляет метрики прогресса загрузки

### Система мониторинга и алертов
1. **monitoring/metrics.go** собирает метрики со всех компонентов
2. **utils/health_check.go** периодически проверяет здоровье системы
3. **workers/health_worker.go** анализирует качество поступающих данных
4. **monitoring/alerts.go** сравнивает метрики с thresholds
5. **services/notification_handler.go** отправляет уведомления в Slack/email
6. **monitoring/dashboard.go** предоставляет real-time view состояния системы
│   

## Описание ключевых компонентов

### Основные сервисы
- **api/gateway.go** - Принимает все запросы от пользователей, проверяет права доступа
- **services/market_ingestion.go** - Подключается к биржам и получает новые данные
- **services/data_processor.go** - Превращает сырые данные в удобный формат (свечи)
- **services/task_scheduler.go** - Решает когда какие задачи запускать

### Воркеры (фоновые процессы)
- **workers/sync_worker.go** - Быстро получает данные когда пользователь их запросил
- **workers/backfill_worker.go** - Медленно заполняет пропуски в исторических данных
- **workers/health_worker.go** - Следит чтобы все работало правильно

### Хранение данных
- **database/** - Все что связано с основной базой данных PostgreSQL
- **cache/** - Быстрое хранилище Redis для часто используемых данных
- **queue/** - Очередь задач в DragonflyDB

### Интеграции
- **exchanges/** - Подключения ко всем биржам (Binance, Coinbase и др.)
- **auth/** - Проверка пользователей и их прав доступа

### Вспомогательное
- **utils/** - Полезные функции (логирование, проверки, ограничения)
- **monitoring/** - Следит за работой системы и предупреждает о проблемах