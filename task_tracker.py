#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Скрипт для отслеживания и автоматического выполнения задач из development_plan.md
"""

import os
import time
import subprocess
from pathlib import Path

class TaskTracker:
    def __init__(self, plan_file="development_plan.md"):
        self.plan_file = plan_file
        self.completed_tasks = []
        self.current_task = None
        
    def read_plan(self):
        """Читает план разработки из файла"""
        try:
            with open(self.plan_file, 'r', encoding='utf-8') as f:
                return f.read()
        except FileNotFoundError:
            print(f"Файл {self.plan_file} не найден")
            return None
            
    def parse_tasks(self, plan_content):
        """Парсит задачи из плана"""
        tasks = []
        lines = plan_content.split('\n')
        current_section = ""
        current_week = ""
        
        for line in lines:
            if line.startswith('## '):
                current_section = line[3:].strip()
            elif line.startswith('### '):
                current_week = line[4:].strip()
            elif line.startswith('- [ ]') or line.startswith('- [x]'):
                task_status = line[3:6]  # [ ] или [x]
                task_description = line[6:].strip()
                task_info = {
                    'section': current_section,
                    'week': current_week,
                    'description': task_description,
                    'completed': task_status == '[x]'
                }
                tasks.append(task_info)
                
        return tasks
        
    def find_next_task(self, tasks):
        """Находит следующую незавершенную задачу"""
        for task in tasks:
            if not task['completed']:
                return task
        return None
        
    def mark_task_completed(self, task):
        """Отмечает задачу как выполненную в файле _planprd.md"""
        # Здесь будет логика отметки задачи как выполненной
        # Пока просто выводим информацию о выполненной задаче
        print(f"Задача выполнена: {task['description']}")
        
    def execute_task(self, task):
        """Выполняет конкретную задачу"""
        print(f"Выполняю задачу: {task['description']}")
        # Здесь будет логика выполнения задачи
        # Пока просто имитируем выполнение
        time.sleep(2)
        self.mark_task_completed(task)
        
    def run(self):
        """Основной цикл выполнения задач"""
        plan_content = self.read_plan()
        if not plan_content:
            return
            
        tasks = self.parse_tasks(plan_content)
        next_task = self.find_next_task(tasks)
        
        if next_task:
            self.current_task = next_task
            self.execute_task(next_task)
        else:
            print("Все задачи выполнены!")

if __name__ == "__main__":
    tracker = TaskTracker()
    tracker.run()
