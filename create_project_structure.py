import os
import json

def create_directories(directories):
    """Создает все необходимые директории проекта"""
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"Создана директория: {directory}")

def create_files(files):
    """Создает все необходимые файлы проекта"""
    for file_path in files:
        # Создаем директории для файла, если они не существуют
        directory = os.path.dirname(file_path)
        if directory:
            os.makedirs(directory, exist_ok=True)
        
        # Создаем пустой файл
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write("")
        print(f"Создан файл: {file_path}")

def main():
    """Основная функция для создания структуры проекта"""
    # Читаем структуру проекта из JSON файла
    with open('project_structure.json', 'r', encoding='utf-8') as f:
        structure = json.load(f)
    
    # Создаем директории
    create_directories(structure['directories'])
    
    # Создаем файлы
    create_files(structure['files'])
    
    print("\nСтруктура проекта успешно создана!")

if __name__ == "__main__":
    main()
