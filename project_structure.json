{"directories": ["api", "auth", "cache", "configs", "configs/grafana", "configs/grafana/dashboards", "database", "database/migrations", "docs", "exchanges", "monitoring", "queue", "scripts", "services", "utils", "workers"], "files": ["main.go", "config.yaml", ".env.example", ".giti<PERSON>re", "go.mod", "go.sum", "<PERSON><PERSON><PERSON>", "README.md", "api/gateway.go", "api/middleware.go", "api/routes.go", "api/websocket.go", "auth/api_keys.go", "auth/jwt_handler.go", "auth/permissions.go", "cache/candle_cache.go", "cache/redis_client.go", "cache/ticker_cache.go", "configs/alertmanager.yml", "configs/grafana/dashboards/business_metrics.json", "configs/grafana/dashboards/system_overview.json", "configs/jaeger.yml", "configs/prometheus.yml", "database/connection.go", "database/models.go", "database/queries.go", "database/migrations/001_create_tickers.sql", "database/migrations/002_create_candles.sql", "database/migrations/003_create_tasks.sql", "database/migrations/004_create_users.sql", "database/migrations/005_create_analytics.sql", "docs/api_documentation.md", "docs/architecture_overview.md", "docs/deployment_guide.md", "docs/operation_guide.md", "exchanges/base_exchange.go", "exchanges/binance_client.go", "exchanges/coinbase_client.go", "exchanges/exchange_manager.go", "exchanges/kraken_client.go", "monitoring/alerts.go", "monitoring/dashboard.go", "monitoring/metrics.go", "queue/event_bus.go", "queue/priority_queue.go", "queue/task_queue.go", "scripts/backup.sh", "scripts/deploy.sh", "scripts/monitor.sh", "scripts/setup_database.sh", "scripts/stress_test.sh", "services/data_processor.go", "services/historical_manager.go", "services/market_ingestion.go", "services/notification_handler.go", "services/task_scheduler.go", "utils/health_check.go", "utils/logger.go", "utils/rate_limiter.go", "utils/validator.go", "workers/backfill_worker.go", "workers/cleanup_worker.go", "workers/health_worker.go", "workers/sync_worker.go"]}